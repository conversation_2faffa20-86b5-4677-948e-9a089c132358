'use server';
import { db } from '@/app/db';
import { eq, and, desc } from 'drizzle-orm'
import { bots } from '@/app/db/schema';
import { getWorkspaceFromFormData, workspaceValidation, createErrorResponse, createSuccessResponse } from '@/app/utils/workspace';

export const addBotInServer = async (
  workspaceId: string,
  botInfo: {
    title: string;
    desc?: string;
    prompt: string;
    avatar: string;
    avatarType: 'emoji' | 'url';
  }
) => {
  // 验证 workspace 权限
  const validation = await workspaceValidation(workspaceId);
  if (validation.status !== 'success') {
    return validation;
  }

  // 验证必需字段
  if (!botInfo.title || !botInfo.prompt || !botInfo.avatar || !botInfo.avatarType) {
    return createErrorResponse('Missing required fields');
  }

  try {
    const botResult = await db.insert(bots)
      .values({
        ...botInfo,
        creator: 'public',
        workspaceId
      })
      .returning();

    return createSuccessResponse(botResult[0]);
  } catch (error) {
    console.error('Error creating bot:', error);
    return createErrorResponse('Failed to create bot');
  }
}

export const deleteBotInServer = async (formData: FormData) => {
  // 验证 workspace 权限
  const workspaceValidation = await getWorkspaceFromFormData(formData);
  if ('error' in workspaceValidation) {
    return workspaceValidation;
  }

  const { workspaceId } = workspaceValidation as { workspaceId: string; userId: string; role: string };
  const botId = parseInt(formData.get('botId') as string);

  if (!botId) {
    return createErrorResponse('Bot ID is required');
  }

  try {
    await db.delete(bots)
      .where(
        and(
          eq(bots.id, botId),
          eq(bots.workspaceId, workspaceId)
        ));

    return createSuccessResponse();
  } catch (error) {
    console.error('Error deleting bot:', error);
    return createErrorResponse('Failed to delete bot');
  }
}

export const getBotListInServer = async (workspaceId: string) => {
  // 验证 workspace 权限
  const formData = new FormData();
  formData.append('workspaceId', workspaceId);
  const workspaceValidation = await getWorkspaceFromFormData(formData);
  if ('error' in workspaceValidation) {
    return workspaceValidation;
  }

  const { workspaceId: validatedWorkspaceId } = workspaceValidation as { workspaceId: string; userId: string; role: string };

  try {
    const result = await db.select()
      .from(bots)
      .where(eq(bots.workspaceId, validatedWorkspaceId))
      .orderBy(desc(bots.createdAt));

    return createSuccessResponse(result);
  } catch (error) {
    console.error('Error fetching bot list:', error);
    return createErrorResponse('Failed to fetch bot list');
  }
}

export const getBotInfoInServer = async (botId: number, workspaceId: string) => {
  // 验证 workspace 权限
  const formData = new FormData();
  formData.append('workspaceId', workspaceId);
  const workspaceValidation = await getWorkspaceFromFormData(formData);
  if ('error' in workspaceValidation) {
    return workspaceValidation;
  }

  const { workspaceId: validatedWorkspaceId } = workspaceValidation as { workspaceId: string; userId: string; role: string };

  try {
    const result = await db.select()
      .from(bots)
      .where(
        and(
          eq(bots.id, botId),
          eq(bots.workspaceId, validatedWorkspaceId)
        )
      );

    if (result.length > 0) {
      return createSuccessResponse(result[0]);
    } else {
      return createErrorResponse('Bot not found');
    }
  } catch (error) {
    console.error('Error fetching bot info:', error);
    return createErrorResponse('Failed to fetch bot info');
  }
}